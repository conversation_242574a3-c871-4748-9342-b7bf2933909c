import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"
import { demoMode } from "./demo-mode"
import type { SupabaseClient } from "@supabase/supabase-js"

// Singleton pour le client Supabase côté serveur
let serverSupabaseInstance: SupabaseClient | null = null

export async function createServerSupabaseClient() {
  // Si nous sommes en mode démo, ne pas initialiser Supabase
  if (demoMode.enabled) {
    console.log("Mode démo activé, client Supabase serveur non initialisé")
    return null
  }

  // Si nous avons déjà une instance, la retourner
  if (serverSupabaseInstance) {
    return serverSupabaseInstance
  }

  const cookieStore = await cookies()
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn("Variables d'environnement Supabase manquantes")
    return null
  }

  serverSupabaseInstance = createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
      set(name: string, value: string, options: any) {
        cookieStore.set({ name, value, ...options })
      },
      remove(name: string, options: any) {
        cookieStore.set({ name, value: '', ...options })
      },
    },
  })

  return serverSupabaseInstance
}
